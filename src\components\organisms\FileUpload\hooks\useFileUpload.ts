import { useCallback, useRef, useState } from 'react';
import { useNotify } from 'react-admin';

import { UploadedFile } from '~/types/fileUpload';
import { fileUploadManager } from '~/utils/FileUploadManager';
import { FileUploadConfig, UploadState } from '../types';
import { mapFileTypeToCode } from '../utils/fileUploadHelpers';

/**
 * Hook for managing file upload operations
 */
export const useFileUpload = (config: FileUploadConfig) => {
  const [state, setState] = useState<UploadState>({
    uploading: false,
    progress: 0,
    queue: [],
    errors: [],
  });

  const notify = useNotify();
  const abortControllerRef = useRef<AbortController | null>(null);

  // Update upload state
  const updateState = useCallback((updates: Partial<UploadState>) => {
    setState(prev => ({ ...prev, ...updates }));
  }, []);

  // Set uploading state
  const setUploading = useCallback(
    (uploading: boolean) => {
      updateState({ uploading });
    },
    [updateState]
  );

  // Set progress
  const setProgress = useCallback(
    (progress: number) => {
      updateState({ progress });
    },
    [updateState]
  );

  // Add error
  const addError = useCallback(
    (error: Error) => {
      updateState({ errors: [...state.errors, error] });
    },
    [updateState, state.errors]
  );

  // Clear errors
  const clearErrors = useCallback(() => {
    updateState({ errors: [] });
  }, [updateState]);

  // Check if file type should use image variants (folder structure)
  const shouldUseImageVariants = useCallback(
    (files: File[]) => {
      // Use variants for 'images' fileType or 'public' fileType with image files and target sizes
      if (config.fileType === 'images') {
        return true; // Always use variants for 'images' fileType
      }

      if (
        config.fileType === 'public' &&
        config.imageConfig?.targetSizes?.length
      ) {
        // Check if any files are images
        return files.some(file => file.type.startsWith('image/'));
      }

      return false;
    },
    [config.fileType, config.imageConfig?.targetSizes]
  );

  // Upload files with variants (for public images)
  const uploadPublicImagesWithVariants = useCallback(
    async (files: File[]): Promise<UploadedFile[]> => {
      const targetSizes = config.imageConfig?.targetSizes || [];
      const standardSizes = targetSizes.map((ts: any) => ({
        key: ts.key || ts.name || `${ts.width}x${ts.height}`,
        name: ts.name || ts.key || `${ts.width}×${ts.height}`,
        width: ts.width,
        height: ts.height,
        aspectRatio: ts.width / ts.height,
        description: `Generated variant ${ts.width}×${ts.height}`,
      }));

      const uploadPromises = files.map(async (file, index) => {
        const onProgress = (progress: number) => {
          // Calculate overall progress across all files
          const fileProgress =
            (index / files.length) * 100 + progress / files.length;
          setProgress(fileProgress);
        };

        return fileUploadManager.createInMemoryImageWithVariants(
          file,
          standardSizes,
          config.imageConfig?.quality || 0.9,
          onProgress
        );
      });

      return Promise.all(uploadPromises);
    },
    [config.imageConfig, setProgress]
  );

  // Create in-memory files (replaces uploadToTemp)
  const uploadToTemp = useCallback(
    async (files: File[]): Promise<UploadedFile[]> => {
      const uploadPromises = files.map(async (file, index) => {
        const fileProgress = ((index + 1) / files.length) * 100;
        setProgress(fileProgress);

        return fileUploadManager.createInMemoryFile(
          file,
          mapFileTypeToCode(config.fileType)
        );
      });

      return Promise.all(uploadPromises);
    },
    [config.fileType, setProgress]
  );

  // Main upload function
  const upload = useCallback(
    async (files: File[]): Promise<UploadedFile[]> => {
      if (files.length === 0) return [];

      // Create abort controller for cancellation
      abortControllerRef.current = new AbortController();

      setUploading(true);
      setProgress(0);
      clearErrors();

      try {
        let uploadedFiles: UploadedFile[];

        if (shouldUseImageVariants(files)) {
          // Use client-side processing for images with variants (images + public with target sizes)
          uploadedFiles = await uploadPublicImagesWithVariants(files);
        } else {
          // Use traditional upload for other file types
          uploadedFiles = await uploadToTemp(files);
        }

        setProgress(100);

        // Call success callback
        config.callbacks?.onUploadSuccess?.(uploadedFiles);

        // Call individual file uploaded callbacks
        uploadedFiles.forEach(file => {
          config.callbacks?.onFileUploaded?.(file);
        });

        return uploadedFiles;
      } catch (error) {
        // Handle upload errors
        let errorMessage = 'An error occurred during file upload';

        if (error instanceof Error) {
          if (
            error.message.includes('FileUploadManager not properly initialized')
          ) {
            errorMessage =
              'Upload service is not ready. Please refresh the page and try again.';
          } else {
            errorMessage = error.message;
          }
        }

        const uploadError =
          error instanceof Error ? error : new Error(errorMessage);
        addError(uploadError);
        notify(errorMessage, { type: 'error' });
        config.callbacks?.onUploadError?.(uploadError);

        return [];
      } finally {
        setUploading(false);
        abortControllerRef.current = null;
      }
    },
    [
      shouldUseImageVariants,
      uploadPublicImagesWithVariants,
      uploadToTemp,
      setUploading,
      setProgress,
      clearErrors,
      addError,
      notify,
      config.callbacks,
    ]
  );

  // Upload single file
  const uploadSingle = useCallback(
    async (file: File): Promise<UploadedFile> => {
      const result = await upload([file]);
      return result[0];
    },
    [upload]
  );

  // Upload image with pre-cropped variants
  const uploadImageWithCroppedVariants = useCallback(
    async (originalFile: File, croppedFiles: File[]): Promise<UploadedFile> => {
      if (!originalFile) throw new Error('Original file is required');

      setUploading(true);
      setProgress(0);
      clearErrors();

      try {
        const uploadedFile =
          await fileUploadManager.createInMemoryImageWithCroppedVariants(
            originalFile,
            croppedFiles,
            config.imageConfig?.quality || 0.9,
            setProgress
          );

        // Call callbacks
        config.callbacks?.onFileUploaded?.(uploadedFile);
        config.callbacks?.onUploadSuccess?.([uploadedFile], {
          isImageEdit: true,
        });

        return uploadedFile;
      } catch (error) {
        const uploadError =
          error instanceof Error ? error : new Error('Upload failed');
        addError(uploadError);
        notify(uploadError.message, { type: 'error' });
        config.callbacks?.onUploadError?.(uploadError);
        throw uploadError;
      } finally {
        setUploading(false);
      }
    },
    [
      config.imageConfig,
      config.callbacks,
      setUploading,
      setProgress,
      clearErrors,
      addError,
      notify,
    ]
  );

  // Cancel upload
  const cancelUpload = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
    setUploading(false);
    setProgress(0);
  }, [setUploading, setProgress]);

  return {
    // State
    uploading: state.uploading,
    progress: state.progress,
    queue: state.queue,
    errors: state.errors,

    // Actions
    upload,
    uploadSingle,
    uploadImageWithCroppedVariants,
    cancelUpload,
    clearErrors,

    // Utilities
    shouldUseImageVariants,
  };
};
