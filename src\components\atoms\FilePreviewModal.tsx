import { useEffect, useMemo, useState } from 'react';
import CloseIcon from '@mui/icons-material/Close';
import ContentCopyIcon from '@mui/icons-material/ContentCopy';
import FullscreenIcon from '@mui/icons-material/Fullscreen';
import PauseIcon from '@mui/icons-material/Pause';
import PlayArrowIcon from '@mui/icons-material/PlayArrow';
import RefreshIcon from '@mui/icons-material/Refresh';
import VolumeOffIcon from '@mui/icons-material/VolumeOff';
import VolumeUpIcon from '@mui/icons-material/VolumeUp';
import {
  Box,
  Button,
  Chip,
  CircularProgress,
  Dialog,
  DialogContent,
  IconButton,
  Stack,
  Typography,
  useMediaQuery,
  useTheme,
} from '@mui/material';
import { useTranslation } from 'react-i18next';

import { useFileSize } from '~/hooks/useFileSize';
import { useFileUrl } from '~/hooks/useFileUrl';
import { UploadedFile } from '~/types/fileUpload';
import { isPublicFile } from '~/utils/bucketManager';
import { formatFileSize } from '~/utils/fileSizeUtils';
import {
  generateVariantUrl,
  getBestVariantForContext,
  getImageVariants,
  getVariantDisplayName,
  hasImageVariants,
  sortVariantsByImportance,
} from '~/utils/imageVariants';
import {
  generatePublicImageUrl,
  getOptimalImageVariant,
} from '~/utils/urlGeneration';
import TempFilePreviewBlocked from './TempFilePreviewBlocked';

// URL cache for variants to prevent flickering
const variantUrlCache = new Map<string, string>();
const MAX_CACHE_SIZE = 50; // Limit cache size to prevent memory issues

const addToCache = (key: string, url: string) => {
  // If cache is at limit, remove oldest entry
  if (variantUrlCache.size >= MAX_CACHE_SIZE) {
    const firstKey = variantUrlCache.keys().next().value;
    if (firstKey) {
      variantUrlCache.delete(firstKey);
    }
  }
  variantUrlCache.set(key, url);
};

interface FilePreviewModalProps {
  file: UploadedFile | null;
  onClose: () => void;
  initialVariant?: string;
}

// Helper function to check if file is a video
const isVideoFile = (file: UploadedFile): boolean => {
  const videoExtensions = [
    'mp4',
    'webm',
    'ogg',
    'avi',
    'mov',
    'wmv',
    'flv',
    'm4v',
  ];
  return videoExtensions.includes(file.e.toLowerCase());
};

// Helper function to check if file is an image
const isImageFile = (file: UploadedFile): boolean => {
  const imageExtensions = [
    'jpg',
    'jpeg',
    'png',
    'gif',
    'bmp',
    'webp',
    'svg',
    'tiff',
    'ico',
  ];
  return imageExtensions.includes(file.e.toLowerCase());
};

/**
 * Modal component for full-size file preview with automatic URL management
 * Enhanced with image variant support for public images
 */
export function FilePreviewModal({
  file,
  onClose,
  initialVariant,
}: FilePreviewModalProps) {
  const { t } = useTranslation();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Initialize selectedVariant with the correct value from the start
  const [selectedVariant, setSelectedVariant] = useState<string | 'original'>(
    () => {
      console.log(
        '🐛 [FilePreviewModal] Lazy initializer - file:',
        file?.fn,
        'initialVariant:',
        initialVariant
      );
      if (initialVariant) {
        console.log(
          '🐛 [FilePreviewModal] Using initialVariant:',
          initialVariant
        );
        return initialVariant;
      }
      if (file && hasImageVariants(file) && isImageFile(file)) {
        console.log(
          '🐛 [FilePreviewModal] Setting initial variant to thumbnail'
        );
        return 'thumbnail';
      }
      console.log(
        '🐛 [FilePreviewModal] Setting initial variant to empty string'
      );
      return '';
    }
  );
  const [availableVariants, setAvailableVariants] = useState<string[]>([]);

  // Video player state
  const [isPlaying, setIsPlaying] = useState<boolean>(false);
  const [isMuted, setIsMuted] = useState<boolean>(false);
  const [showControls, setShowControls] = useState<boolean>(true);
  const [videoRef, setVideoRef] = useState<HTMLVideoElement | null>(null);

  // Copy URL state
  const [copySuccess, setCopySuccess] = useState<boolean>(false);

  // Image zoom state
  const [zoomLevel, setZoomLevel] = useState<number>(1);
  const [imagePosition, setImagePosition] = useState<{ x: number; y: number }>({
    x: 0,
    y: 0,
  });
  const [isDragging, setIsDragging] = useState<boolean>(false);
  const [dragStart, setDragStart] = useState<{ x: number; y: number }>({
    x: 0,
    y: 0,
  });

  // Touch/pinch state for mobile
  const [initialPinchDistance, setInitialPinchDistance] = useState<number>(0);
  const [initialZoom, setInitialZoom] = useState<number>(1);

  // Get translated variant display name
  const getTranslatedVariantDisplayName = (variant: string): string => {
    if (variant === 'original') {
      return t('imageSizes.original', 'Original');
    }

    // Get the basic translated name
    const translatedName = t(`imageSizes.${variant}`, variant);

    // Get dimensions from getVariantDisplayName and extract them
    const originalDisplayName = getVariantDisplayName(variant);
    const dimensionsMatch = originalDisplayName.match(/\((\d+×\d+)\)/);
    const dimensions = dimensionsMatch ? dimensionsMatch[1] : '';

    return dimensions ? `${translatedName} (${dimensions})` : translatedName;
  };

  // Check if this file has the new image variants structure
  const hasVariants = file ? hasImageVariants(file) : false;
  const isVideo = file ? isVideoFile(file) : false;
  const isImage = file ? isImageFile(file) : false;

  // Copy URL to clipboard function
  const handleCopyUrl = async () => {
    const currentDisplayUrl = displayUrl;
    if (!currentDisplayUrl) return;

    try {
      await navigator.clipboard.writeText(currentDisplayUrl);
      setCopySuccess(true);
      setTimeout(() => setCopySuccess(false), 2000); // Reset after 2 seconds
    } catch (err) {
      console.error('Failed to copy URL:', err);
    }
  };

  // Cleanup variant URL cache when file changes or component unmounts
  useEffect(() => {
    return () => {
      // Clean up cache entries for this file when component unmounts
      if (file) {
        for (const [key] of variantUrlCache.entries()) {
          if (key.startsWith(file.fn)) {
            variantUrlCache.delete(key);
          }
        }
      }

      // Clean up video player
      if (videoRef) {
        videoRef.pause();
        setVideoRef(null);
        setIsPlaying(false);
      }
    };
  }, [file, videoRef]);

  // Video control functions
  const handlePlayPause = () => {
    if (videoRef) {
      if (isPlaying) {
        videoRef.pause();
      } else {
        videoRef.play();
      }
      setIsPlaying(!isPlaying);
    }
  };

  const handleMuteToggle = () => {
    if (videoRef) {
      videoRef.muted = !isMuted;
      setIsMuted(!isMuted);
    }
  };

  const handleFullscreen = () => {
    if (videoRef && videoRef.requestFullscreen) {
      videoRef.requestFullscreen();
    }
  };

  // Video event handlers
  const handleVideoLoad = (video: HTMLVideoElement | null) => {
    // Clean up previous video's event listeners if any
    if (videoRef) {
      videoRef.removeEventListener('play', handlePlay);
      videoRef.removeEventListener('pause', handlePause);
      videoRef.removeEventListener('volumechange', handleVolumeChange);
    }

    setVideoRef(video);
    if (video) {
      video.addEventListener('play', handlePlay);
      video.addEventListener('pause', handlePause);
      video.addEventListener('volumechange', handleVolumeChange);
    }
  };

  // Event handler functions (defined outside to enable cleanup)
  const handlePlay = () => setIsPlaying(true);
  const handlePause = () => setIsPlaying(false);
  const handleVolumeChange = () => {
    if (videoRef) {
      setIsMuted(videoRef.muted);
    }
  };

  // Auto-hide controls for video
  useEffect(() => {
    if (!isVideo || !showControls) return;

    const timer = setTimeout(() => {
      if (isPlaying) {
        setShowControls(false);
      }
    }, 3000);

    return () => clearTimeout(timer);
  }, [isVideo, isPlaying, showControls]);

  // Cleanup video event listeners on unmount
  useEffect(() => {
    return () => {
      if (videoRef) {
        videoRef.removeEventListener('play', handlePlay);
        videoRef.removeEventListener('pause', handlePause);
        videoRef.removeEventListener('volumechange', handleVolumeChange);
      }
    };
  }, [videoRef]);

  // Show controls when mouse moves over video
  const handleMouseMove = () => {
    if (isVideo) {
      setShowControls(true);
    }
  };

  const handleImageMouseDown = (e: React.MouseEvent) => {
    if (zoomLevel > 1) {
      e.preventDefault();
      setIsDragging(true);
      setDragStart({
        x: e.clientX - imagePosition.x,
        y: e.clientY - imagePosition.y,
      });
    }
  };

  const handleImageMouseMove = (e: React.MouseEvent) => {
    if (isDragging && zoomLevel > 1) {
      setImagePosition({
        x: e.clientX - dragStart.x,
        y: e.clientY - dragStart.y,
      });
    }
    // Also handle video controls
    if (isVideo) {
      setShowControls(true);
    }
  };

  const handleImageMouseUp = () => {
    setIsDragging(false);
  };

  // Touch handlers for mobile pinch-to-zoom
  const getTouchDistance = (touches: React.TouchList) => {
    if (touches.length < 2) return 0;
    const touch1 = touches[0];
    const touch2 = touches[1];
    return Math.sqrt(
      Math.pow(touch2.clientX - touch1.clientX, 2) +
        Math.pow(touch2.clientY - touch1.clientY, 2)
    );
  };

  const handleTouchStart = (e: React.TouchEvent) => {
    if (e.touches.length === 1 && zoomLevel > 1) {
      // Single touch - start dragging
      const touch = e.touches[0];
      setIsDragging(true);
      setDragStart({
        x: touch.clientX - imagePosition.x,
        y: touch.clientY - imagePosition.y,
      });
    } else if (e.touches.length === 2) {
      // Two touches - start pinching
      setIsDragging(false);
      const distance = getTouchDistance(e.touches);
      setInitialPinchDistance(distance);
      setInitialZoom(zoomLevel);
    }
  };

  const handleTouchMove = (e: React.TouchEvent) => {
    e.preventDefault(); // Prevent scrolling

    if (e.touches.length === 1 && isDragging && zoomLevel > 1) {
      // Single touch - dragging
      const touch = e.touches[0];
      setImagePosition({
        x: touch.clientX - dragStart.x,
        y: touch.clientY - dragStart.y,
      });
    } else if (e.touches.length === 2 && initialPinchDistance > 0) {
      // Two touches - pinching
      const currentDistance = getTouchDistance(e.touches);
      const scale = currentDistance / initialPinchDistance;
      const newZoom = initialZoom * scale;

      if (newZoom <= 1) {
        setImagePosition({ x: 0, y: 0 });
        setZoomLevel(1);
      } else {
        setZoomLevel(Math.min(newZoom, 5));
      }
    }
  };

  const handleTouchEnd = () => {
    setIsDragging(false);
    setInitialPinchDistance(0);
    setInitialZoom(1);
  };

  // Reset zoom when variant changes
  useEffect(() => {
    setZoomLevel(1);
    setImagePosition({ x: 0, y: 0 });
  }, [selectedVariant]);

  // Load available variants async and preload common ones
  useEffect(() => {
    if (hasVariants && file && isImage) {
      getImageVariants(file).then(variants => {
        const sortedVariants = sortVariantsByImportance(variants);
        setAvailableVariants(sortedVariants);

        // Only auto-select if no variant is currently selected and no initial variant was provided
        // This prevents overriding the thumbnail selection made in the file change effect
        if (sortedVariants.length > 0 && !initialVariant && !selectedVariant) {
          setSelectedVariant(sortedVariants[0]);
        }

        // Preload the most common variants to reduce loading time
        // Use the first few available variants instead of hardcoding
        const commonVariants = sortedVariants.slice(0, 3); // Get the first 3 most important variants
        commonVariants.forEach(variant => {
          const cacheKey = `${file.fn}_${variant}`;
          if (!variantUrlCache.has(cacheKey)) {
            generateVariantUrl(file, variant)
              .then(url => {
                addToCache(cacheKey, url);
              })
              .catch(error => {
                console.warn(`Failed to preload variant "${variant}":`, error);
              });
          }
        });
      });
    } else {
      setAvailableVariants([]);
    }
  }, [hasVariants, file, selectedVariant, initialVariant]);

  // Reset selected variant when file changes
  useEffect(() => {
    if (file && !initialVariant) {
      if (hasVariants && isImage) {
        // For images with variants, immediately set to thumbnail to prevent original flash
        // The variants loading effect will update available variants but keep this selection
        setSelectedVariant('thumbnail');
      } else {
        // For files without variants, always select original
        setSelectedVariant('original');
      }
    } else if (initialVariant) {
      // Use the provided initial variant
      setSelectedVariant(initialVariant);
    } else if (!file) {
      // Reset when no file
      setSelectedVariant('');
    }
  }, [file, hasVariants, isImage, initialVariant]);

  // For all files (both variant and non-variant), use standard URL generation with placeholder support
  // Pass the selected variant to get the correct URL (works for both in-memory and storage files)
  const urlOptions = useMemo(() => {
    if (selectedVariant && hasVariants && isImage) {
      return { imageVariant: selectedVariant as any };
    }
    return undefined;
  }, [selectedVariant, hasVariants, isImage]);

  const {
    url: standardUrl,
    loading,
    error,
    refresh,
  } = useFileUrl(file, urlOptions);

  console.log(
    '🐛 [FilePreviewModal] useFileUrl result - standardUrl:',
    standardUrl?.substring(0, 50),
    'urlOptions:',
    urlOptions
  );

  // Variant loading is now handled by useFileUrl with urlOptions
  // No need for separate variantUrl state

  // Get file size at runtime for the selected variant (only for images with variants)
  const { formattedSize, loading: sizeLoading } = useFileSize(file, {
    formatted: true,
    precision: 1,
    variant: hasVariants && isImage ? selectedVariant : undefined,
  });

  // Use appropriate URL based on variant system
  // Now that urlOptions includes variant for all files, standardUrl handles variants correctly
  const displayUrl = (() => {
    // For all files, use standardUrl which now includes proper variant handling
    const url = standardUrl;
    console.log(
      '🐛 [FilePreviewModal] displayUrl computed:',
      url?.substring(0, 50),
      'selectedVariant:',
      selectedVariant
    );
    return url;
  })();

  // Debug logging to check temp file placeholder generation
  useEffect(() => {
    if (file) {
      const debugInfo = {
        filename: `${file.fn}.${file.e}`,
        fileType: file.t,
        isTemporary: file.x,
        hasVariants,
        requestingVariant: !!selectedVariant,
        variantName: selectedVariant || 'original',
        standardUrl: !!standardUrl,
        displayUrl: !!displayUrl,
        loading,
        error: error?.message,
      };
      console.log('🐛 [FilePreviewModal] File preview state:', debugInfo);

      // Log URL details for the actual displayed URL
      const actualUrl = displayUrl || standardUrl;
      console.log('🐛 [FilePreviewModal] URL details:', {
        usingDisplayUrl: !!displayUrl,
        usingStandardUrl: !displayUrl && !!standardUrl,
        hasActualUrl: !!actualUrl,
        urlLength: actualUrl?.length || 0,
        urlStart: actualUrl?.substring(0, 50) + '...',
        urlType: actualUrl?.startsWith('data:image/svg+xml')
          ? 'placeholder'
          : actualUrl?.startsWith('https://')
            ? 'real'
            : 'unknown',
      });
    }
  }, [
    file,
    standardUrl,
    displayUrl,
    loading,
    error,
    selectedVariant,
    hasVariants,
  ]);

  // Simplified loading logic - just use the standardUrl loading state
  // since we're now using standardUrl for all files with proper variant options
  const isMainLoading = loading;

  // Handle wheel event with proper passive: false to allow preventDefault
  useEffect(() => {
    const handleWheel = (e: WheelEvent) => {
      e.preventDefault();
      const delta = e.deltaY > 0 ? 0.9 : 1.1;
      setZoomLevel(prev => {
        const newZoom = prev * delta;
        if (newZoom <= 1) {
          setImagePosition({ x: 0, y: 0 });
          return 1;
        }
        return Math.min(newZoom, 5);
      });
    };

    const imageContainer = document.getElementById('image-zoom-container');
    if (imageContainer && isImage && displayUrl) {
      imageContainer.addEventListener('wheel', handleWheel, { passive: false });
      return () => {
        imageContainer.removeEventListener('wheel', handleWheel);
      };
    }
  }, [isImage, displayUrl]);

  return (
    <Dialog
      open={!!file}
      onClose={onClose}
      maxWidth={isMobile ? false : 'lg'}
      fullWidth={!isMobile}
      fullScreen={isMobile}
      sx={{
        '& .MuiDialog-paper': {
          backgroundColor: 'rgba(0, 0, 0, 0.9)',
          boxShadow: 'none',
          ...(isMobile
            ? {
                // Mobile: Full screen
                height: '100vh',
                maxHeight: 'none',
                margin: 0,
              }
            : {
                // Desktop: Fixed size leaving 10-15% margin
                height: '85vh',
                maxHeight: '85vh',
                width: '85vw',
                maxWidth: '85vw',
                margin: 'auto',
              }),
        },
        '& .MuiBackdrop-root': {
          backgroundColor: 'rgba(0, 0, 0, 0.8)',
        },
      }}
      TransitionProps={{
        timeout: 300,
      }}
    >
      <DialogContent
        sx={{
          p: 0,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          position: 'relative',
          height: '100%', // Use full height of fixed modal
          backgroundColor: 'transparent',
          overflow: 'hidden', // Prevent content from overflowing fixed size
        }}
      >
        {file && (
          <>
            <IconButton
              onClick={onClose}
              sx={{
                position: 'absolute',
                top: 16,
                left: 16, // Always position on the left side for consistent modal UI
                color: 'white',
                backgroundColor: 'rgba(0, 0, 0, 0.5)',
                '&:hover': {
                  backgroundColor: 'rgba(0, 0, 0, 0.7)',
                },
                zIndex: 1,
              }}
            >
              <CloseIcon />
            </IconButton>

            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                width: '100%',
                height: '100%',
                gap: isMobile ? 1 : 1.5, // Moderate gap on desktop
                py: isMobile ? 2 : 2.5, // Moderate padding on desktop
                overflow: isMobile ? 'auto' : 'hidden', // Prevent scrolling on desktop
              }}
            >
              {isMainLoading && (
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                  <CircularProgress size={40} sx={{ color: 'white' }} />
                  <Typography variant="h6" sx={{ color: 'white' }}>
                    Loading {isVideo ? 'video' : 'image'}...
                  </Typography>
                </Box>
              )}
              {error && (
                <Box sx={{ textAlign: 'center' }}>
                  <Typography variant="h6" sx={{ color: 'white', mb: 2 }}>
                    Failed to load {isVideo ? 'video' : 'image'}
                  </Typography>
                  <Button
                    onClick={refresh}
                    variant="contained"
                    startIcon={<RefreshIcon />}
                    sx={{ mb: 2 }}
                  >
                    Retry
                  </Button>
                </Box>
              )}
              {displayUrl && !isMainLoading && !error && (
                <>
                  {/* Main media display */}
                  <Box
                    sx={{
                      position: 'relative',
                      display: 'flex',
                      justifyContent: 'center',
                      alignItems: 'center',
                      flex: 1, // Take up available space
                      width: '100%',
                    }}
                    onMouseMove={handleImageMouseMove}
                  >
                    {isImage ? (
                      /* Image Display with Zoom Support */
                      <Box
                        id="image-zoom-container"
                        sx={{
                          position: 'relative',
                          overflow: 'hidden',
                          // Dynamic container size based on zoom level
                          width: zoomLevel > 1 ? '100%' : 'auto',
                          height: zoomLevel > 1 ? '100%' : 'auto',
                          maxWidth: isMobile ? '90vw' : 'calc(85vw - 48px)',
                          maxHeight:
                            hasVariants && availableVariants.length > 0
                              ? isMobile
                                ? 'calc(100vh - 320px)'
                                : 'calc(85vh - 280px)'
                              : isMobile
                                ? 'calc(100vh - 220px)'
                                : 'calc(85vh - 160px)',
                          borderRadius: 1,
                          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
                          cursor:
                            zoomLevel > 1
                              ? isDragging
                                ? 'grabbing'
                                : 'grab'
                              : 'default',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                        onMouseDown={handleImageMouseDown}
                        onMouseUp={handleImageMouseUp}
                        onMouseLeave={handleImageMouseUp}
                        onTouchStart={handleTouchStart}
                        onTouchMove={handleTouchMove}
                        onTouchEnd={handleTouchEnd}
                      >
                        <Box
                          component="img"
                          key={`${file.fn}-${selectedVariant}-${displayUrl?.substring(0, 20)}`}
                          src={displayUrl}
                          alt={file.rn}
                          onLoad={() =>
                            console.log(
                              '🐛 [FilePreviewModal] Image loaded with URL:',
                              displayUrl?.substring(0, 50)
                            )
                          }
                          onError={() =>
                            console.log(
                              '🐛 [FilePreviewModal] Image failed to load URL:',
                              displayUrl?.substring(0, 50)
                            )
                          }
                          sx={{
                            // Base size - let the image maintain its natural aspect ratio
                            maxWidth:
                              zoomLevel <= 1
                                ? isMobile
                                  ? '90vw'
                                  : 'calc(85vw - 48px)'
                                : 'none',
                            maxHeight:
                              zoomLevel <= 1
                                ? hasVariants && availableVariants.length > 0
                                  ? isMobile
                                    ? 'calc(100vh - 320px)'
                                    : 'calc(85vh - 280px)'
                                  : isMobile
                                    ? 'calc(100vh - 220px)'
                                    : 'calc(85vh - 160px)'
                                : 'none',
                            width:
                              zoomLevel > 1 ? `${100 * zoomLevel}%` : 'auto',
                            height:
                              zoomLevel > 1 ? `${100 * zoomLevel}%` : 'auto',
                            objectFit: 'contain',
                            transform:
                              zoomLevel > 1
                                ? `translate(${imagePosition.x}px, ${imagePosition.y}px)`
                                : 'none',
                            transformOrigin: 'center center',
                            opacity: loading ? 0.7 : 1,
                            transition: isDragging
                              ? 'none'
                              : 'opacity 0.2s ease-in-out, transform 0.2s ease-out',
                            userSelect: 'none',
                            pointerEvents: 'none',
                          }}
                        />
                      </Box>
                    ) : isVideo ? (
                      /* Video Display */
                      <Box
                        sx={{
                          position: 'relative',
                          maxWidth: isMobile ? '90vw' : 'calc(85vw - 48px)',
                          maxHeight: isMobile
                            ? 'calc(100vh - 220px)'
                            : 'calc(85vh - 160px)',
                          borderRadius: 1,
                          overflow: 'hidden',
                          boxShadow: '0 4px 20px rgba(0, 0, 0, 0.3)',
                        }}
                      >
                        <Box
                          component="video"
                          ref={handleVideoLoad}
                          src={displayUrl}
                          controls={false} // We'll use custom controls
                          sx={{
                            width: '100%',
                            height: '100%',
                            maxWidth: '100%',
                            maxHeight: '100%',
                            objectFit: 'contain',
                            backgroundColor: 'black',
                          }}
                          onClick={handlePlayPause}
                        />

                        {/* Custom Video Controls */}
                        {showControls && (
                          <Box
                            sx={{
                              position: 'absolute',
                              bottom: 0,
                              left: 0,
                              right: 0,
                              background:
                                'linear-gradient(transparent, rgba(0,0,0,0.8))',
                              display: 'flex',
                              alignItems: 'center',
                              gap: 1,
                              p: 2,
                              transition: 'opacity 0.3s ease-in-out',
                            }}
                          >
                            <IconButton
                              onClick={handlePlayPause}
                              sx={{
                                color: 'white',
                                '&:hover': {
                                  backgroundColor: 'rgba(255,255,255,0.1)',
                                },
                              }}
                            >
                              {isPlaying ? <PauseIcon /> : <PlayArrowIcon />}
                            </IconButton>
                            <IconButton
                              onClick={handleMuteToggle}
                              sx={{
                                color: 'white',
                                '&:hover': {
                                  backgroundColor: 'rgba(255,255,255,0.1)',
                                },
                              }}
                            >
                              {isMuted ? <VolumeOffIcon /> : <VolumeUpIcon />}
                            </IconButton>
                            <Box sx={{ flex: 1 }} /> {/* Spacer */}
                            <IconButton
                              onClick={handleFullscreen}
                              sx={{
                                color: 'white',
                                '&:hover': {
                                  backgroundColor: 'rgba(255,255,255,0.1)',
                                },
                              }}
                            >
                              <FullscreenIcon />
                            </IconButton>
                          </Box>
                        )}

                        {/* Play button overlay for paused state */}
                        {!isPlaying && (
                          <Box
                            sx={{
                              position: 'absolute',
                              top: '50%',
                              left: '50%',
                              transform: 'translate(-50%, -50%)',
                              backgroundColor: 'rgba(0,0,0,0.6)',
                              borderRadius: '50%',
                              width: 80,
                              height: 80,
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              cursor: 'pointer',
                              transition: 'all 0.2s ease-in-out',
                              '&:hover': {
                                backgroundColor: 'rgba(0,0,0,0.8)',
                                transform: 'translate(-50%, -50%) scale(1.1)',
                              },
                            }}
                            onClick={handlePlayPause}
                          >
                            <PlayArrowIcon
                              sx={{ color: 'white', fontSize: 40 }}
                            />
                          </Box>
                        )}
                      </Box>
                    ) : (
                      /* Unsupported file type */
                      <Box
                        sx={{
                          textAlign: 'center',
                          p: 4,
                          border: '2px dashed rgba(255,255,255,0.3)',
                          borderRadius: 2,
                          maxWidth: '400px',
                        }}
                      >
                        <Typography variant="h6" sx={{ color: 'white', mb: 2 }}>
                          Preview not available
                        </Typography>
                        <Typography
                          variant="body2"
                          sx={{ color: 'rgba(255,255,255,0.7)' }}
                        >
                          File type "{file.e}" is not supported for preview.
                        </Typography>
                      </Box>
                    )}

                    {/* Loading overlay for variant changes */}
                    {loading && (
                      <Box
                        sx={{
                          position: 'absolute',
                          top: '50%',
                          left: '50%',
                          transform: 'translate(-50%, -50%)',
                          display: 'flex',
                          alignItems: 'center',
                          gap: 1,
                          backgroundColor: 'rgba(0, 0, 0, 0.8)',
                          borderRadius: 2,
                          px: 2,
                          py: 1,
                        }}
                      >
                        <CircularProgress size={20} sx={{ color: 'white' }} />
                        <Typography variant="body2" sx={{ color: 'white' }}>
                          Loading variant...
                        </Typography>
                      </Box>
                    )}
                  </Box>

                  {/* Variant selection moved below image - only for images */}
                  {isImage && hasVariants && (
                    <Box
                      sx={{
                        width: '100%',
                        px: isMobile ? 2 : 3,
                        maxWidth: isMobile ? '100%' : '90%', // More generous width on desktop
                        py: isMobile ? 0 : 1, // Extra vertical padding on desktop
                        minHeight: isMobile ? '60px' : '80px', // Reserve space to prevent layout shift
                        display: 'flex',
                        flexDirection: 'column',
                        alignItems: 'center',
                        justifyContent: 'center',
                      }}
                    >
                      {availableVariants.length > 0 ? (
                        <>
                          <Typography
                            variant="body2"
                            sx={{
                              color: 'white',
                              mb: isMobile ? 1 : 1.5,
                              textAlign: 'center',
                              fontSize: isMobile ? '0.75rem' : '0.9rem', // Slightly larger on desktop
                              fontWeight: 500, // Make it slightly bolder
                            }}
                          >
                            {t('filePreview.availableSizes')}
                          </Typography>
                          <Stack
                            direction="row"
                            spacing={1}
                            sx={{
                              justifyContent: 'center',
                              flexWrap: 'wrap',
                              gap: isMobile ? 0.5 : 2, // Much more generous spacing on desktop
                              minHeight: isMobile ? 'auto' : '40px', // Ensure minimum height on desktop
                            }}
                          >
                            {hasVariants ? (
                              // New variant system
                              <>
                                {availableVariants
                                  .filter(v => v !== 'original')
                                  .map(variant => (
                                    <Chip
                                      key={variant}
                                      label={getTranslatedVariantDisplayName(
                                        variant
                                      )}
                                      variant={
                                        selectedVariant === variant
                                          ? 'filled'
                                          : 'outlined'
                                      }
                                      onClick={() =>
                                        setSelectedVariant(variant as any)
                                      }
                                      sx={{
                                        color: 'white',
                                        borderColor: 'rgba(255, 255, 255, 0.5)',
                                        '&.MuiChip-filled': {
                                          backgroundColor: 'primary.main',
                                        },
                                        '&:hover': {
                                          backgroundColor:
                                            selectedVariant === variant
                                              ? 'primary.dark'
                                              : 'rgba(255, 255, 255, 0.1)',
                                          borderColor:
                                            'rgba(255, 255, 255, 0.8)',
                                        },
                                        mb: isMobile ? 0.5 : 1, // More margin on desktop for wrapped chips
                                        fontSize: isMobile
                                          ? '0.7rem'
                                          : '0.875rem', // Normal size on desktop
                                        height: isMobile ? 24 : 32, // Larger on desktop
                                        transition: 'all 0.2s ease-in-out',
                                      }}
                                      size={isMobile ? 'small' : 'medium'}
                                    />
                                  ))}
                                <Chip
                                  label="Original"
                                  variant={
                                    selectedVariant === 'original'
                                      ? 'filled'
                                      : 'outlined'
                                  }
                                  onClick={() => setSelectedVariant('original')}
                                  sx={{
                                    color: 'white',
                                    borderColor: 'rgba(255, 255, 255, 0.5)',
                                    '&.MuiChip-filled': {
                                      backgroundColor: 'primary.main',
                                    },
                                    '&:hover': {
                                      backgroundColor:
                                        selectedVariant === 'original'
                                          ? 'primary.dark'
                                          : 'rgba(255, 255, 255, 0.1)',
                                      borderColor: 'rgba(255, 255, 255, 0.8)',
                                    },
                                    mb: isMobile ? 0.5 : 1, // More margin on desktop for wrapped chips
                                    fontSize: isMobile ? '0.7rem' : '0.875rem', // Normal size on desktop
                                    height: isMobile ? 24 : 32, // Larger on desktop
                                    transition: 'all 0.2s ease-in-out',
                                  }}
                                  size={isMobile ? 'small' : 'medium'}
                                />
                              </>
                            ) : (
                              // Legacy variant system
                              <>
                                <Chip
                                  label="Original"
                                  variant={
                                    selectedVariant === 'original'
                                      ? 'filled'
                                      : 'outlined'
                                  }
                                  onClick={() => setSelectedVariant('original')}
                                  sx={{
                                    color: 'white',
                                    borderColor: 'rgba(255, 255, 255, 0.5)',
                                    '&.MuiChip-filled': {
                                      backgroundColor: 'primary.main',
                                    },
                                    '&:hover': {
                                      backgroundColor:
                                        selectedVariant === 'original'
                                          ? 'primary.dark'
                                          : 'rgba(255, 255, 255, 0.1)',
                                      borderColor: 'rgba(255, 255, 255, 0.8)',
                                    },
                                    mb: isMobile ? 0.5 : 1, // More margin on desktop for wrapped chips
                                    fontSize: isMobile ? '0.7rem' : '0.875rem', // Normal size on desktop
                                    height: isMobile ? 24 : 32, // Larger on desktop
                                    transition: 'all 0.2s ease-in-out',
                                  }}
                                  size={isMobile ? 'small' : 'medium'}
                                />
                              </>
                            )}
                          </Stack>
                        </>
                      ) : (
                        // Loading state - reserve space to prevent layout shift
                        <Box
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: 1,
                            color: 'rgba(255, 255, 255, 0.7)',
                          }}
                        >
                          <CircularProgress
                            size={16}
                            sx={{ color: 'inherit' }}
                          />
                          <Typography
                            variant="body2"
                            sx={{
                              fontSize: isMobile ? '0.75rem' : '0.9rem',
                            }}
                          >
                            Loading sizes...
                          </Typography>
                        </Box>
                      )}
                    </Box>
                  )}
                </>
              )}
              <Typography
                variant={isMobile ? 'body1' : 'h6'}
                sx={{
                  color: 'white',
                  textAlign: 'center',
                  px: 2,
                  fontSize: isMobile ? '1rem' : '1.25rem', // Larger on desktop
                  fontWeight: isMobile ? 400 : 500, // Slightly bolder on desktop
                  mt: isMobile ? 0 : 1, // Extra margin on desktop
                }}
              >
                {file.rn}
              </Typography>{' '}
              {(formattedSize || (file as any).s || sizeLoading) && (
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: 1,
                  }}
                >
                  <Typography
                    variant="body2"
                    sx={{
                      color: 'rgba(255, 255, 255, 0.7)',
                      fontSize: isMobile ? '0.75rem' : '1rem',
                    }}
                  >
                    {sizeLoading ? (
                      `Loading size...`
                    ) : (
                      <>
                        <Box
                          component="span"
                          sx={{
                            display: 'inline-flex',
                            alignItems: 'center',
                            flexWrap: 'nowrap',
                            gap: 0.25,
                          }}
                        >
                          <span>
                            {formattedSize ||
                              ((file as any).s
                                ? formatFileSize((file as any).s * 1024)
                                : '')}{' '}
                            • {file.e}
                          </span>
                          {/* Copy URL inline - only for public files */}
                          {isPublicFile(file) && displayUrl && (
                            <>
                              <span> • </span>
                              <Box
                                component="span"
                                sx={{
                                  display: 'inline-flex',
                                  alignItems: 'center',
                                  gap: 0.25,
                                  cursor: 'pointer',
                                  color: copySuccess
                                    ? 'success.main'
                                    : 'rgba(255, 255, 255, 0.7)',
                                  '&:hover': {
                                    color: 'white',
                                  },
                                  transition: 'color 0.2s ease',
                                  whiteSpace: 'nowrap',
                                }}
                                onClick={handleCopyUrl}
                              >
                                <ContentCopyIcon
                                  sx={{
                                    fontSize: isMobile ? '0.875rem' : '1rem',
                                  }}
                                />
                                <span style={{ fontSize: 'inherit' }}>
                                  {copySuccess
                                    ? t('filePreview.copied')
                                    : t('filePreview.copyUrl')}
                                </span>
                              </Box>
                            </>
                          )}
                        </Box>
                      </>
                    )}
                  </Typography>
                </Box>
              )}
            </Box>
          </>
        )}
      </DialogContent>
    </Dialog>
  );
}
