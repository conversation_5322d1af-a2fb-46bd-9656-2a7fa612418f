import { useCallback, useEffect, useMemo, useState } from 'react';

import { UploadedFile, UrlGenerationOptions } from '../types/fileUpload';
import { getSecureFileUrl } from '../utils/bucketManager';

interface UseFileUrlReturn {
  url: string | null;
  loading: boolean;
  error: Error | null;
  refresh: () => void;
}

// Internal cache to track URL generation timestamps and URLs for fallback
const urlTimestamps = new Map<string, number>();
const urlCache = new Map<string, string>();

// Request deduplication to prevent multiple simultaneous requests for the same file
const pendingRequests = new Map<string, Promise<string>>();

// Cache cleanup configuration
const CACHE_TTL = 10 * 60 * 1000; // 10 minutes
const CLEANUP_INTERVAL = 5 * 60 * 1000; // 5 minutes

// Periodic cache cleanup to prevent memory leaks
setInterval(() => {
  const now = Date.now();

  for (const [key, timestamp] of urlTimestamps.entries()) {
    if (now - timestamp > CACHE_TTL) {
      const url = urlCache.get(key);
      if (url && url.startsWith('blob:')) {
        URL.revokeObjectURL(url);
      }
      urlCache.delete(key);
      urlTimestamps.delete(key);
    }
  }
}, CLEANUP_INTERVAL);

/**
 * Clear cache entries for a specific file when it transitions from temp to permanent
 * This should be called by the file upload system when files are moved
 */
export const clearFileUrlCache = (file: UploadedFile) => {
  // Clear both temp and permanent cache entries for this file
  const tempKey = `temp:${file.t}:${file.fn}.${file.e}:`;
  const permKey = `perm:${file.t}:${file.fn}.${file.e}:`;

  // Clear all variants for this file
  for (const [key, url] of urlCache.entries()) {
    if (key.startsWith(tempKey) || key.startsWith(permKey)) {
      if (url.startsWith('blob:')) {
        URL.revokeObjectURL(url);
      }
      urlCache.delete(key);
      urlTimestamps.delete(key);
    }
  }
};

/**
 * Custom hook for managing file URLs with secure access and temp file blocking
 * Uses the new SecureFileManager for proper security and caching
 */
export function useFileUrl(
  file: UploadedFile | null,
  options?: UrlGenerationOptions
): UseFileUrlReturn {
  const [url, setUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<Error | null>(null);

  // Memoize the options to prevent unnecessary re-renders
  const stableOptions = useMemo(
    () => options,
    [
      options?.imageVariant,
      options?.forceRefresh,
      options?.context?.accountId,
      options?.context?.sellpointId,
      options?.context?.customPath,
    ]
  );

  // Create a more comprehensive cache key that includes bucket type and variant
  const fileKey = useMemo(() => {
    if (!file) return null;

    const bucketType = file.x ? 'temp' : 'perm';
    const variant = stableOptions?.imageVariant || 'original';
    return `${bucketType}:${file.t}:${file.fn}.${file.e}:${variant}`;
  }, [file?.fn, file?.e, file?.t, file?.x, stableOptions?.imageVariant]);

  const generateUrl = useCallback(async () => {
    if (!file || !fileKey) {
      setUrl(null);
      setError(null);
      return;
    }

    // Check if we already have a cached URL for this file key
    const cachedUrl = urlCache.get(fileKey);
    if (cachedUrl && !stableOptions?.forceRefresh) {
      console.log(
        '🐛 [useFileUrl] Using cached URL for fileKey:',
        fileKey,
        'URL starts with:',
        cachedUrl.substring(0, 50)
      );
      setUrl(cachedUrl);
      return;
    }

    console.log(
      '🐛 [useFileUrl] No cache for fileKey:',
      fileKey,
      'generating new URL'
    );

    // Check if there's already a pending request for this file key
    const pendingRequest = pendingRequests.get(fileKey);
    if (pendingRequest && !stableOptions?.forceRefresh) {
      try {
        const url = await pendingRequest;
        setUrl(url);
        return;
      } catch (err) {
        // If pending request failed, continue with new request
      }
    }

    setLoading(true);
    setError(null);

    // Create a new request promise and cache it to prevent duplicates
    const requestPromise = getSecureFileUrl(file, stableOptions);
    pendingRequests.set(fileKey, requestPromise);

    try {
      const newUrl = await requestPromise;
      setUrl(newUrl);

      // Cache the URL for future use
      urlCache.set(fileKey, newUrl);
      urlTimestamps.set(fileKey, Date.now());
    } catch (err) {
      const error = err as Error;
      setError(error);
      setUrl(null);
    } finally {
      setLoading(false);
      // Remove the pending request
      pendingRequests.delete(fileKey);
    }
  }, [file?.fn, file?.e, file?.t, file?.x, fileKey, stableOptions]);

  const refresh = useCallback(() => {
    if (fileKey) {
      urlTimestamps.delete(fileKey);
      urlCache.delete(fileKey);
      pendingRequests.delete(fileKey); // Clear any pending requests too
    }
    generateUrl();
  }, [generateUrl, fileKey]);

  useEffect(() => {
    generateUrl();
  }, [generateUrl]);

  // Detect bucket transitions (temp to permanent) and refresh URLs
  useEffect(() => {
    if (!file || !fileKey) return;

    const handleBucketTransition = () => {
      // If file was temp but now is permanent, or vice versa, refresh URL
      const currentBucketType = file.x ? 'temp' : 'perm';
      const cachedKey = urlCache.get(fileKey);

      if (cachedKey) {
        // Extract bucket type from cached key
        const keyParts = fileKey.split(':');
        const keyBucketType = keyParts[0];

        if (keyBucketType !== currentBucketType) {
          // Bucket type changed, clear old cache and regenerate
          urlCache.delete(fileKey);
          urlTimestamps.delete(fileKey);
          generateUrl();
        }
      }
    };

    handleBucketTransition();
  }, [file?.x, fileKey, generateUrl]); // React to changes in the temp flag

  // Cleanup function to revoke blob URLs when component unmounts or file changes
  useEffect(() => {
    return () => {
      if (fileKey && url && url.startsWith('blob:')) {
        // For in-memory files, don't revoke blob URLs here as they're managed by InMemoryFileManager
        // Only revoke URLs that are not in-memory file blobs (legacy temp files)
        // In-memory files have their own lifecycle management
        const isInMemoryFile = file?.inMemoryData !== undefined;
        if (!isInMemoryFile) {
          // Revoke blob URL if it's no longer needed
          // But keep it in cache for a short time in case it's used elsewhere
          setTimeout(() => {
            if (urlCache.get(fileKey) === url) {
              URL.revokeObjectURL(url);
              urlCache.delete(fileKey);
            }
          }, 1000); // 1 second delay to allow for quick re-use
        }
      }
    };
  }, [fileKey, url, file?.inMemoryData]);

  return { url, loading, error, refresh };
}

/**
 * Example usage in a component:
 *
 * const MyComponent = ({ file }: { file: UploadedFile }) => {
 *   const { url, loading, error, refresh } = useFileUrl(file);
 *
 *   if (loading) return <Spinner />;
 *   if (error) return <Button onClick={refresh}>Retry</Button>;
 *   if (!url) return null;
 *
 *   return <img src={url} alt={file.fn} />;
 * };
 */
