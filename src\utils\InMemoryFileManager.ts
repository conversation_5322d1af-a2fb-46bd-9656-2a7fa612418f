/**
 * In-Memory File Manager
 * Replaces temp bucket storage with client-side blob storage
 * Handles blob URL generation, memory tracking, and cleanup
 */

import {
  IN_MEMORY_FILE_CONFIG,
  InMemoryFileData,
  UploadedFile,
} from '~/types/fileUpload';
import { generateTempFilePlaceholder } from './placeholderGenerator';

/**
 * Memory usage statistics
 */
export interface MemoryUsageStats {
  totalSize: number; // Total memory usage in bytes
  fileCount: number; // Number of files in memory
  blobUrlCount: number; // Number of active blob URLs
  largestFile: number; // Size of largest file in bytes
  oldestFile: number; // Timestamp of oldest file
}

/**
 * Blob URL cache entry
 */
interface BlobUrlCacheEntry {
  url: string;
  fileId: string;
  variant: string;
  createdAt: number;
  size: number;
}

/**
 * In-Memory File Manager
 * Singleton class for managing in-memory files and blob URLs
 */
export class InMemoryFileManager {
  private static instance: InMemoryFileManager;

  // Memory tracking
  private memoryUsage = 0;
  private fileCount = 0;

  // Blob URL management
  private blobUrls = new Map<string, BlobUrlCacheEntry>();
  private fileToUrls = new Map<string, Set<string>>(); // fileId -> Set of cache keys

  // Cleanup management
  private cleanupInterval: NodeJS.Timeout | null = null;
  private lastCleanup = Date.now();

  private constructor() {
    this.startCleanupInterval();
    this.setupMemoryWarnings();
  }

  static getInstance(): InMemoryFileManager {
    if (!InMemoryFileManager.instance) {
      InMemoryFileManager.instance = new InMemoryFileManager();
    }
    return InMemoryFileManager.instance;
  }

  /**
   * Get blob URL for a file variant
   */
  async getUrl(file: UploadedFile, variant?: string): Promise<string> {
    if (!file.inMemoryData) {
      return generateTempFilePlaceholder(
        file,
        variant as 'original' | 'preview'
      );
    }

    const actualVariant = variant || 'original';
    const cacheKey = this.getCacheKey(file.fn, actualVariant);

    // Return cached URL if exists and still valid
    const cached = this.blobUrls.get(cacheKey);
    if (cached && this.isUrlValid(cached)) {
      // For thumbnail variant, always regenerate to ensure correct blob is used
      // This fixes an issue where thumbnail cache could be corrupted during initial generation
      if (actualVariant === 'thumbnail') {
        this.blobUrls.delete(cacheKey);
      } else {
        return cached.url;
      }
    }

    // Get the appropriate blob
    let blob: Blob;
    if (!variant || variant === 'original') {
      blob = file.inMemoryData.original;
      console.log(
        '🐛 [InMemoryFileManager] Using original blob, size:',
        blob.size
      );
    } else if (file.inMemoryData.variants?.has(variant)) {
      blob = file.inMemoryData.variants.get(variant)!;
      console.log(
        `🐛 [InMemoryFileManager] Using ${variant} blob, size:`,
        blob.size
      );

      // Special check for thumbnail - it should be small (< 5KB)
      if (variant === 'thumbnail' && blob.size > 5000) {
        console.error('🚨 [InMemoryFileManager] Thumbnail blob is too large!', {
          variant,
          blobSize: blob.size,
          expectedSize: '< 5KB',
          originalSize: file.inMemoryData.original.size,
        });
      }
    } else {
      // Variant doesn't exist, return placeholder
      return generateTempFilePlaceholder(
        file,
        variant as 'original' | 'preview'
      );
    }

    // Create new blob URL
    const blobUrl = URL.createObjectURL(blob);
    console.log(
      `🐛 [InMemoryFileManager] Created blob URL for ${actualVariant}:`,
      blobUrl.substring(0, 50),
      'blob size:',
      blob.size
    );

    // Cache the URL
    const entry: BlobUrlCacheEntry = {
      url: blobUrl,
      fileId: file.fn,
      variant: variant || 'original',
      createdAt: Date.now(),
      size: blob.size,
    };

    this.blobUrls.set(cacheKey, entry);

    // Track file -> URLs mapping
    if (!this.fileToUrls.has(file.fn)) {
      this.fileToUrls.set(file.fn, new Set());
    }
    this.fileToUrls.get(file.fn)!.add(cacheKey);

    // Enforce URL limits
    this.enforceUrlLimits();

    return blobUrl;
  }

  /**
   * Track memory usage when a file is added
   */
  trackFile(file: UploadedFile): void {
    if (!file.inMemoryData) return;

    let size = file.inMemoryData.original.size;
    if (file.inMemoryData.variants) {
      for (const variant of file.inMemoryData.variants.values()) {
        size += variant.size;
      }
    }

    this.memoryUsage += size;
    this.fileCount++;

    // Check memory limits
    this.checkMemoryLimits();
  }

  /**
   * Clean up all resources for a file
   */
  cleanupFile(file: UploadedFile): void {
    if (!file.inMemoryData) return;

    // Revoke all blob URLs for this file
    const fileUrls = this.fileToUrls.get(file.fn);
    if (fileUrls) {
      for (const cacheKey of fileUrls) {
        const entry = this.blobUrls.get(cacheKey);
        if (entry) {
          URL.revokeObjectURL(entry.url);
          this.blobUrls.delete(cacheKey);
        }
      }
      this.fileToUrls.delete(file.fn);
    }

    // Update memory usage
    let size = file.inMemoryData.original.size;
    if (file.inMemoryData.variants) {
      for (const variant of file.inMemoryData.variants.values()) {
        size += variant.size;
      }
    }
    this.memoryUsage -= size;
    this.fileCount--;

    // Clear in-memory data
    delete file.inMemoryData;
  }

  /**
   * Get current memory usage statistics
   */
  getMemoryUsage(): MemoryUsageStats {
    let largestFile = 0;
    let oldestFile = Date.now();

    for (const entry of this.blobUrls.values()) {
      if (entry.size > largestFile) {
        largestFile = entry.size;
      }
      if (entry.createdAt < oldestFile) {
        oldestFile = entry.createdAt;
      }
    }

    return {
      totalSize: this.memoryUsage,
      fileCount: this.fileCount,
      blobUrlCount: this.blobUrls.size,
      largestFile,
      oldestFile,
    };
  }

  /**
   * Force cleanup of old or excess blob URLs
   */
  forceCleanup(): void {
    const now = Date.now();
    const maxAge = 60 * 60 * 1000; // 60 minutes - match isUrlValid timeout

    // Clean up old URLs
    for (const [cacheKey, entry] of this.blobUrls.entries()) {
      if (now - entry.createdAt > maxAge) {
        URL.revokeObjectURL(entry.url);
        this.blobUrls.delete(cacheKey);

        // Update file mapping
        const fileUrls = this.fileToUrls.get(entry.fileId);
        if (fileUrls) {
          fileUrls.delete(cacheKey);
          if (fileUrls.size === 0) {
            this.fileToUrls.delete(entry.fileId);
          }
        }
      }
    }

    this.lastCleanup = now;
  }

  /**
   * Clean up all resources (call on app shutdown)
   */
  destroy(): void {
    // Stop cleanup interval
    if (this.cleanupInterval) {
      clearInterval(this.cleanupInterval);
      this.cleanupInterval = null;
    }

    // Revoke all blob URLs
    for (const entry of this.blobUrls.values()) {
      URL.revokeObjectURL(entry.url);
    }

    // Clear all maps
    this.blobUrls.clear();
    this.fileToUrls.clear();

    // Reset counters
    this.memoryUsage = 0;
    this.fileCount = 0;
  }

  // Private helper methods

  private getCacheKey(fileId: string, variant: string): string {
    return `${fileId}_${variant}`;
  }

  private isUrlValid(entry: BlobUrlCacheEntry): boolean {
    // URLs are valid for 60 minutes to prevent premature cleanup during variant switching
    const maxAge = 60 * 60 * 1000;
    return Date.now() - entry.createdAt < maxAge;
  }

  private enforceUrlLimits(): void {
    if (this.blobUrls.size <= IN_MEMORY_FILE_CONFIG.maxBlobUrls) {
      return;
    }

    // Remove oldest URLs first
    const entries = Array.from(this.blobUrls.entries()).sort(
      ([, a], [, b]) => a.createdAt - b.createdAt
    );

    const toRemove = entries.slice(
      0,
      entries.length - IN_MEMORY_FILE_CONFIG.maxBlobUrls
    );

    for (const [cacheKey, entry] of toRemove) {
      URL.revokeObjectURL(entry.url);
      this.blobUrls.delete(cacheKey);

      // Update file mapping
      const fileUrls = this.fileToUrls.get(entry.fileId);
      if (fileUrls) {
        fileUrls.delete(cacheKey);
        if (fileUrls.size === 0) {
          this.fileToUrls.delete(entry.fileId);
        }
      }
    }
  }

  private checkMemoryLimits(): void {
    if (this.memoryUsage > IN_MEMORY_FILE_CONFIG.memoryWarningThreshold) {
      console.warn(
        `In-memory file storage approaching limit: ${(this.memoryUsage / 1024 / 1024).toFixed(1)}MB / ${(IN_MEMORY_FILE_CONFIG.maxMemoryUsage / 1024 / 1024).toFixed(1)}MB`
      );
    }

    if (this.fileCount > IN_MEMORY_FILE_CONFIG.fileCountWarningThreshold) {
      console.warn(
        `High number of in-memory files: ${this.fileCount} / ${IN_MEMORY_FILE_CONFIG.maxConcurrentFiles}`
      );
    }
  }

  private startCleanupInterval(): void {
    this.cleanupInterval = setInterval(() => {
      this.forceCleanup();
    }, IN_MEMORY_FILE_CONFIG.cleanupInterval);
  }

  private setupMemoryWarnings(): void {
    // Monitor memory usage and warn if approaching limits
    if (
      typeof window !== 'undefined' &&
      'performance' in window &&
      'memory' in (window.performance as any)
    ) {
      setInterval(() => {
        const memory = (window.performance as any).memory;
        if (memory && memory.usedJSHeapSize > 100 * 1024 * 1024) {
          // 100MB
          console.warn('High JavaScript memory usage detected:', {
            used: `${(memory.usedJSHeapSize / 1024 / 1024).toFixed(1)}MB`,
            total: `${(memory.totalJSHeapSize / 1024 / 1024).toFixed(1)}MB`,
            inMemoryFiles: this.getMemoryUsage(),
          });
        }
      }, 60000); // Check every minute
    }
  }
}

// Export singleton instance
export const inMemoryFileManager = InMemoryFileManager.getInstance();
